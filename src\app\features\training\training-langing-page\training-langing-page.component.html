<app-loader [loading]="loading"></app-loader>

<div class="training-wrapper h-full">
  <div class="header py-5">
    <div class="max-w-6xl mx-auto px-4 flex justify-between relative">
      <div class="section w-[450px]">
        <div class="w-100 overflow-hidden text-ellipsis whitespace-nowrap">
          {{ this.projectName }}
        </div>
        <div class="page text-3xl">Training</div>
      </div>

      <div class="section search-bar absolute right-[200px]">
        <app-search-header></app-search-header>
      </div>

      <div class="section">
        <button
          mat-flat-button
          class="whitespace-nowrap flex items-center h-12"
          (click)="openDialog()">
          <mat-icon>add</mat-icon>
          <span> New Training </span>
        </button>
      </div>
    </div>
  </div>
  <div
    class="body"
    style="height: 550px; overflow-y: scroll"
    scrollTracker
    #scrollContainer>
    <!-- Content container for centering -->
    <div class="max-w-6xl mx-auto px-4">
      <!-- {{mlProjects == mlProjectInitialState}} -->
      @if (!isTrainingEmpty) {
        @for (time of timeframe; track time) {
          <div class="mb-2">
            @if (getMlProjectAsPerTimeline(time).length > 0) {
              <div class="flex justify-between mb-2">
                <div class="timeline">
                  {{ getTimelineTitle(time) }}
                </div>
                <div>
                  {{ getTimelineDateRange(time) }}
                </div>
              </div>
              <div class="card">
                @for (project of getMlProjectAsPerTimeline(time); track project) {
                  <app-training-model-cards
                    (updateTraining)="updateTraining($event)"
                    [projectInfo]="project"></app-training-model-cards>
                }
              </div>
            }
          </div>
        }
      } @else {
        <app-no-trainings-card></app-no-trainings-card>
      }

      <div #sentinel class="sentinel" style="height: 1px"></div>
    </div>
  </div>
</div>
