<div class="common-spinner" *ngIf="loaderService.isLoading$ | async">
  <app-loader></app-loader>
</div>
<!-- #0D263905 -->
<!-- #F1F1F3 -->
 <!-- #eeeff9 -->
<section
  class="dashboard-wrappers grid grid-cols-[auto,1fr] h-screen w-screen overflow-hidden mat-app-background">
  <!-- Sidebar Area-->
  <div class="h-full">
    <app-side-bar [page]="currPage"></app-side-bar>
  </div>
  <!-- Main Content Area -->
  <div class="grid grid-rows-[auto,1fr]">
    <!-- App Header -->
    <div class="px-1">
      <app-header></app-header>
    </div>

    <!-- Dynamic Outlet -->
    <div class="px-1 overflow-auto">
      <router-outlet></router-outlet>
    </div>
  </div>
</section>
