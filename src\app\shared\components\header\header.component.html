<div class="flex flex-row justify-end align-items-center px-5 h-[80px]">
  <button
    mat-icon-button
    class="text-center rounded-full w-10 h-10 bg-white"
    [matMenuTriggerFor]="menu">
    <mat-icon class="text-[#296197]">notifications_none</mat-icon>
  </button>
  <mat-menu #menu="matMenu" class="w-[400px] h-88">
    <mat-card class="w-[400px] rounded-md col-span-12 md:col-span-4">
      <mat-card-header class="border-b border-gray-300 flex justify-start pt-0">
        <mat-card-title class="text-gray-800 text-center w-full py-3">
          Fast Actions & Notifications
        </mat-card-title>
      </mat-card-header>
      <mat-card-content>
        @for (notification of notificationLists; track notification) {
          <div
            class="flex items-center border my-3 rounded-xl"
            tabindex="0"
            role="button"
            (click)="
              notification.callFunction
                ? notification.callFunction()
                : redirectToLink(notification.link)
            "
            (keyup.enter)="
              notification.callFunction
                ? notification.callFunction()
                : redirectToLink(notification.link)
            ">
            <div class="flex items-center gap-2 px-4 py-5 flex-1">
              <div
                class="w-10 h-10 text-white rounded-full bg-[#296197] flex items-center justify-center">
                {{ notification.initial }}
              </div>
              <div class="flex flex-col">
                <p>{{ notification.title }}</p>
                <p class="font-normal">{{ notification.message }}</p>
              </div>
            </div>
            <div class="flex items-center justify-center bg-gray-50 w-20 h-20">
              <img
                src="assets/project/Frame2156.svg"
                alt="Cute illustration"
                class="w-20 h-20 mb-4" />
            </div>
          </div>
        }
      </mat-card-content>
    </mat-card>
  </mat-menu>
</div>
