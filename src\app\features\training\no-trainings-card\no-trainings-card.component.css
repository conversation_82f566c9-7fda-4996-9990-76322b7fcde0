.wrapper {
  background-color: #f7f9ff;
  border-radius: 16px;
  height: 461px;
}
.wrapper-content {
  padding: 32px;
}
.text-gray {
  color: #8895a7;
  text-align: center;
}

/* Icon container styling - Hexagon shape */
.icon-container {
  width: 80px;
  height: 80px;
  background-color: #e8e9f3;
  display: flex;
  justify-content: center;
  align-items: center;
  position: relative;
  clip-path: polygon(25% 0%, 75% 0%, 100% 50%, 75% 100%, 25% 100%, 0% 50%);
}

.training-icon {
  font-size: 40px;
  width: 40px;
  height: 40px;
  color: white;
}

/* Description text styling */
.description-text {
  font-family: 'Open Sans', sans-serif;
  font-weight: 500;
  font-size: 16px;
  line-height: 170%;
  letter-spacing: 2%;
  text-align: center;
}
